'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import KBAvatar from '@/components/ui/custom/KbAvatar';
import { Button } from 'antd';
import { UserResponse } from '@/types/user.types';
import { ROUTES } from '@/lib/routes';
import Link from 'next/link';
import {
  FaBuilding,
  FaCog,
  FaCreditCard,
  FaEnvelope,
  FaHome,
  FaIdCard,
  FaKey,
  FaMapMarkerAlt,
  FaPassport,
  FaPhone,
  FaShieldAlt,
  FaUserTie,
} from 'react-icons/fa';

interface ProfileContentProps {
  user: UserResponse;
}

const ProfileContent: React.FC<ProfileContentProps> = ({ user }) => {
  const userInitials = `${user.first_name.charAt(0)}${user.last_name.charAt(0)}`;
  const allIdentities = user.identities || [];
  const allAddresses = user.adresses || [];
  const allPhones = user.phones || [];
  const allPassports = user.passports || [];
  const allVisas = user.visas || [];
  const allLoyalties = user.loyalties || [];

  const maskIdentityNumber = (identityNumber: string) => {
    if (identityNumber.length <= 6) return identityNumber;
    const start = identityNumber.slice(0, 3);
    const end = identityNumber.slice(-3);
    const middle = '*'.repeat(identityNumber.length - 6);
    return `${start}${middle}${end}`;
  };

  return (
    <div className="m-4 p-4">
      <div className="space-y-6">
        {/* Modern Profile Header */}
        <div className="relative overflow-hidden rounded-2xl bg-white shadow-xl">
          {/* Gradient Background */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600"></div>

          {/* Content */}
          <div className="relative z-10 p-8">
            <div className="flex flex-col items-center space-y-6 lg:flex-row lg:space-x-8 lg:space-y-0">
              {/* Avatar Section */}
              <div className="relative">
                <KBAvatar
                  src={`https://ui-avatars.com/api/?name=${user.first_name}+${user.last_name}&background=random`}
                  fallback={userInitials}
                  size={100}
                  className="ring-4 ring-white/30 shadow-2xl"
                />
                <div
                  className={`absolute -right-1 -bottom-1 h-7 w-7 rounded-full border-4 border-white shadow-lg ${
                    user.status === 'active' ? 'bg-green-500' : 'bg-red-500'
                  }`}
                />
              </div>

              {/* User Info */}
              <div className="flex-1 text-center lg:text-left">
                <h1 className="mb-3 text-3xl font-bold text-white">
                  {user.first_name} {user.last_name}
                </h1>
                <div className="mb-4 space-y-2">
                  <div className="flex items-center justify-center text-white/90 lg:justify-start">
                    <FaEnvelope className="mr-3 h-5 w-5" />
                    <span className="text-lg">{user.email}</span>
                  </div>
                  {user.job_title && (
                    <div className="flex items-center justify-center text-white/80 lg:justify-start">
                      <FaUserTie className="mr-3 h-5 w-5" />
                      <span>{user.job_title.name}</span>
                    </div>
                  )}
                  {user.department && (
                    <div className="flex items-center justify-center text-white/80 lg:justify-start">
                      <FaBuilding className="mr-3 h-5 w-5" />
                      <span>{user.department.name}</span>
                    </div>
                  )}
                </div>

                {/* Status Badges */}
                <div className="flex flex-wrap items-center justify-center gap-3 lg:justify-start">
                  <span
                    className={`inline-flex items-center rounded-full px-4 py-2 text-sm font-semibold ${
                      user.status === 'active'
                        ? 'bg-green-500 text-white'
                        : 'bg-red-500 text-white'
                    }`}
                  >
                    {user.status === 'active' ? '✓ Aktif' : '✗ Pasif'}
                  </span>
                  {user.email_verified_at && (
                    <span className="inline-flex items-center rounded-full bg-white/20 px-4 py-2 text-sm font-semibold text-white backdrop-blur-sm">
                      <FaShieldAlt className="mr-2 h-4 w-4" />
                      Doğrulandı
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Password Management Section */}
          <div className="border-t border-white/20 bg-gradient-to-r from-white/15 via-white/10 to-white/15 p-6 backdrop-blur-md">
            <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <div className="flex h-14 w-14 items-center justify-center rounded-2xl bg-gradient-to-br from-amber-400 to-orange-500 shadow-lg">
                    <FaKey className="h-6 w-6 text-white" />
                  </div>
                  <div className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-green-500 border-2 border-white flex items-center justify-center">
                    <FaShieldAlt className="h-2.5 w-2.5 text-white" />
                  </div>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white mb-1">Güvenlik & Şifre</h3>
                  <div className="flex flex-wrap items-center gap-3">
                    <div className="flex items-center rounded-full bg-green-500/20 px-3 py-1 backdrop-blur-sm">
                      <div className="mr-2 h-2 w-2 rounded-full bg-green-400"></div>
                      <span className="text-sm font-semibold text-green-300">Güçlü Şifre</span>
                    </div>
                    <span className="text-sm text-white/70">Son güncelleme: 15.10.2024</span>
                  </div>
                </div>
              </div>

              <div className="flex flex-wrap gap-3">
                <Button
                  type="primary"
                  size="middle"
                  className="rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 border-0 text-white shadow-lg hover:shadow-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 font-semibold"
                >
                  🔐 Şifre Değiştir
                </Button>
                <Button
                  type="default"
                  size="middle"
                  className="rounded-xl bg-white/20 border-white/30 text-white hover:bg-white/30 backdrop-blur-sm transition-all duration-300 font-semibold"
                >
                  🛡️ 2FA Ayarları
                </Button>
                <Link href={ROUTES.PROFILE.PASSWORD}>
                  <Button
                    type="primary"
                    size="middle"
                    icon={<FaCog />}
                    className="rounded-xl bg-gradient-to-r from-indigo-500 to-purple-600 border-0 text-white shadow-lg hover:shadow-xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 font-semibold"
                  >
                    Yönet
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Content Sections */}
        <div className="space-y-6">
          <div className="flex flex-col gap-6 lg:flex-row lg:items-start">
            {/* Left Column */}
            <div className="flex-1 space-y-6">
              <Card className="border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md">
                <CardHeader className="border-b border-gray-100 pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
                        <FaIdCard className="h-5 w-5 text-blue-600" />
                      </div>
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        Kimlik Bilgileri ({allIdentities.length})
                      </CardTitle>
                    </div>
                    <Link href={ROUTES.PROFILE.IDENTITY.INDEX}>
                      <Button
                        type="primary"
                        size="small"
                        icon={<FaCog />}
                        className="flex items-center"
                      >
                        Yönet
                      </Button>
                    </Link>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  {allIdentities.length > 0 ? (
                    <div className="space-y-4">
                      {allIdentities.map((identity, index) => (
                        <div key={identity.id} className={`${index > 0 ? 'border-t pt-4' : ''}`}>
                          <div className="mb-2 flex items-center justify-between">
                            <span className="text-sm font-medium text-gray-700">
                              Kimlik {index + 1}
                            </span>
                            {identity.is_default && (
                              <span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                                Varsayılan
                              </span>
                            )}
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Kimlik No:</span>
                            <span className="font-medium">
                              {maskIdentityNumber(identity.identity_number)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Uyruk:</span>
                            <span className="font-medium">{identity.nationality_code}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-4 text-center">
                      <span className="text-gray-500">Henüz kimlik bilgisi eklenmemiş</span>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md">
                <CardHeader className="border-b border-gray-100 pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-green-100">
                        <FaHome className="h-5 w-5 text-green-600" />
                      </div>
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        Adres Bilgileri ({allAddresses.length})
                      </CardTitle>
                    </div>
                    <Link href={ROUTES.PROFILE.ADDRESS.INDEX}>
                      <Button
                        type="primary"
                        size="small"
                        icon={<FaCog />}
                        className="flex items-center"
                      >
                        Yönet
                      </Button>
                    </Link>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {allAddresses.length > 0 ? (
                    <div className="space-y-4">
                      {allAddresses.map((address, index) => (
                        <div key={address.id} className={`${index > 0 ? 'border-t pt-4' : ''}`}>
                          <div className="mb-2 flex items-center justify-between">
                            <h4 className="flex items-center font-semibold text-gray-700">
                              <FaMapMarkerAlt className="mr-1 text-green-500" />
                              {address.type === 'home'
                                ? 'Ev Adresi'
                                : address.type === 'work'
                                  ? 'İş Adresi'
                                  : 'Diğer Adres'}
                            </h4>
                            {address.is_default && (
                              <span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                                Varsayılan
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">{address.address_line_1}</p>
                          {address.address_line_2 && (
                            <p className="text-sm text-gray-600">{address.address_line_2}</p>
                          )}
                          <p className="text-sm text-gray-600">
                            {address.neighborhood_street && `${address.neighborhood_street}, `}
                            {address.city || 'Bilinmiyor'} {address.postal_code || ''}
                          </p>
                          <p className="text-xs text-gray-500">{address.country_code}</p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-4 text-center">
                      <p className="text-gray-500">Henüz adres bilgisi eklenmemiş.</p>
                      <Link href={ROUTES.PROFILE.ADDRESS.CREATE}>
                        <Button type="link" size="small" className="mt-2">
                          Adres Ekle
                        </Button>
                      </Link>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md">
                <CardHeader className="border-b border-gray-100 pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-orange-100">
                        <FaPhone className="h-5 w-5 text-orange-600" />
                      </div>
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        İletişim Bilgileri ({allPhones.length})
                      </CardTitle>
                    </div>
                    <Link href={ROUTES.PROFILE.PHONE.INDEX}>
                      <Button
                        type="primary"
                        size="small"
                        icon={<FaCog />}
                        className="flex items-center"
                      >
                        Yönet
                      </Button>
                    </Link>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {allPhones.length > 0 ? (
                    <div className="space-y-4">
                      {allPhones.map((phone, index) => (
                        <div key={phone.id} className={`${index > 0 ? 'border-t pt-4' : ''}`}>
                          <div className="mb-2 flex items-center justify-between">
                            <h4 className="flex items-center font-semibold text-gray-700">
                              <FaPhone className="mr-1 text-orange-500" />
                              {phone.type === 'home'
                                ? 'Ev Telefonu'
                                : phone.type === 'mobile'
                                  ? 'Cep Telefonu'
                                  : 'Acil Telefon'}
                            </h4>
                            {phone.is_default && (
                              <span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                                Varsayılan
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">
                            {phone.country_code} {phone.phone_number}
                          </p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-4 text-center">
                      <p className="text-gray-500">Henüz telefon bilgisi eklenmemiş.</p>
                      <Link href={ROUTES.PROFILE.PHONE.CREATE}>
                        <Button type="link" size="small" className="mt-2">
                          Telefon Ekle
                        </Button>
                      </Link>
                    </div>
                  )}
                  <hr />
                  <div className="flex justify-between">
                    <span className="text-gray-600">Email:</span>
                    <span className="font-medium">{user.email}</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right Column */}
            <div className="flex-1 space-y-6">
              <Card className="border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md">
                <CardHeader className="border-b border-gray-100 pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100">
                        <FaPassport className="h-5 w-5 text-purple-600" />
                      </div>
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        Pasaport Bilgileri ({allPassports.length})
                      </CardTitle>
                    </div>
                    <Link href={ROUTES.PROFILE.PASSPORT.INDEX}>
                      <Button
                        type="primary"
                        size="small"
                        icon={<FaCog />}
                        className="flex items-center"
                      >
                        Yönet
                      </Button>
                    </Link>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {allPassports.length > 0 ? (
                    <div className="space-y-4">
                      {allPassports.map((passport, index) => (
                        <div key={passport.id} className={`${index > 0 ? 'border-t pt-4' : ''}`}>
                          <div className="mb-2 flex items-center justify-between">
                            <h4 className="flex items-center font-semibold text-gray-700">
                              <FaPassport className="mr-1 text-purple-500" />
                              Pasaport {index + 1}
                            </h4>
                            {passport.is_default && (
                              <span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                                Varsayılan
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">{passport.passport_number}</p>
                          <p className="text-xs text-gray-500">{passport.nationality}</p>
                          <div className="mt-2 text-xs text-gray-500">
                            Geçerlilik: {new Date(passport.expiry_date).toLocaleDateString('tr-TR')}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-4 text-center">
                      <p className="text-gray-500">Henüz pasaport bilgisi eklenmemiş.</p>
                      <Link href={ROUTES.PROFILE.PASSPORT.CREATE}>
                        <Button type="link" size="small" className="mt-2">
                          Pasaport Ekle
                        </Button>
                      </Link>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md">
                <CardHeader className="border-b border-gray-100 pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-teal-100">
                        <FaPassport className="h-5 w-5 text-teal-600" />
                      </div>
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        Vize Bilgileri ({allVisas.length})
                      </CardTitle>
                    </div>
                    <Link href={ROUTES.PROFILE.VISA.INDEX}>
                      <Button
                        type="primary"
                        size="small"
                        icon={<FaCog />}
                        className="flex items-center"
                      >
                        Yönet
                      </Button>
                    </Link>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {allVisas.length > 0 ? (
                    <div className="space-y-4">
                      {allVisas.map((visa, index) => (
                        <div key={visa.id} className={`${index > 0 ? 'border-t pt-4' : ''}`}>
                          <div className="mb-2 flex items-center justify-between">
                            <h4 className="flex items-center font-semibold text-gray-700">
                              <FaPassport className="mr-1 text-teal-500" />
                              Vize {index + 1}
                            </h4>
                            {visa.is_default && (
                              <span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                                Varsayılan
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">{visa.document_number}</p>
                          <p className="text-xs text-gray-500">
                            {visa.country} - {visa.visa_type === 'tourist' ? 'Turistik' : 'İş'}
                          </p>
                          <div className="mt-2 text-xs text-gray-500">
                            Geçerlilik: {new Date(visa.end_date).toLocaleDateString('tr-TR')}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-4 text-center">
                      <p className="text-gray-500">Henüz vize bilgisi eklenmemiş.</p>
                      <Link href={ROUTES.PROFILE.VISA.CREATE}>
                        <Button type="link" size="small" className="mt-2">
                          Vize Ekle
                        </Button>
                      </Link>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md">
                <CardHeader className="border-b border-gray-100 pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-yellow-100">
                        <FaCreditCard className="h-5 w-5 text-yellow-600" />
                      </div>
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        Sadakat Programı ({allLoyalties.length})
                      </CardTitle>
                    </div>
                    <Link href={ROUTES.PROFILE.LOYALTY.INDEX}>
                      <Button
                        type="primary"
                        size="small"
                        icon={<FaCog />}
                        className="flex items-center"
                      >
                        Yönet
                      </Button>
                    </Link>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {allLoyalties.length > 0 ? (
                    <div className="space-y-4">
                      {allLoyalties.map((loyalty, index) => (
                        <div key={loyalty.id} className={`${index > 0 ? 'border-t pt-4' : ''}`}>
                          <div className="mb-2 flex items-center justify-between">
                            <h4 className="flex items-center font-semibold text-gray-700">
                              <FaCreditCard className="mr-1 text-yellow-500" />
                              {loyalty.airline?.airline_name || 'Bilinmeyen Havayolu'}
                            </h4>
                            {loyalty.is_default && (
                              <span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                                Varsayılan
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">Kart No: {loyalty.card_number}</p>
                          <p className="text-xs text-gray-500">
                            {loyalty.airline?.airline_code || '-'}
                          </p>
                          <div className="mt-2 text-xs text-gray-500">
                            Geçerlilik: {new Date(loyalty.end_date).toLocaleDateString('tr-TR')}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-4 text-center">
                      <p className="text-gray-500">Henüz sadakat programı eklenmemiş.</p>
                      <Link href={ROUTES.PROFILE.LOYALTY.CREATE}>
                        <Button type="link" size="small" className="mt-2">
                          Sadakat Programı Ekle
                        </Button>
                      </Link>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileContent;
