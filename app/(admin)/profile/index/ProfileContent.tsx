'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import KBAvatar from '@/components/ui/custom/KbAvatar';
import { Button } from 'antd';
import { UserResponse } from '@/types/user.types';
import { ROUTES } from '@/lib/routes';
import Link from 'next/link';
import {
  FaBuilding,
  FaCog,
  FaCreditCard,
  FaEnvelope,
  FaHome,
  FaIdCard,
  FaKey,
  FaMapMarkerAlt,
  FaPassport,
  FaPhone,
  FaShieldAlt,
  FaUserTie,
} from 'react-icons/fa';

interface ProfileContentProps {
  user: UserResponse;
}

const ProfileContent: React.FC<ProfileContentProps> = ({ user }) => {
  const userInitials = `${user.first_name.charAt(0)}${user.last_name.charAt(0)}`;
  const allIdentities = user.identities || [];
  const allAddresses = user.adresses || [];
  const allPhones = user.phones || [];
  const allPassports = user.passports || [];
  const allVisas = user.visas || [];
  const allLoyalties = user.loyalties || [];

  const maskIdentityNumber = (identityNumber: string) => {
    if (identityNumber.length <= 6) return identityNumber;
    const start = identityNumber.slice(0, 3);
    const end = identityNumber.slice(-3);
    const middle = '*'.repeat(identityNumber.length - 6);
    return `${start}${middle}${end}`;
  };

  return (
    <div className="m-4 p-4">
      <div className="space-y-6">
        {/* Compact Profile Header */}
        <Card className="border-0 bg-white shadow-lg">
          <CardHeader className="p-6">
            <div className="flex items-center justify-between">
              {/* Left Side - Avatar & Basic Info */}
              <div className="flex items-center space-x-6">
                <div className="relative">
                  <KBAvatar
                    src={`https://ui-avatars.com/api/?name=${user.first_name}+${user.last_name}&background=random`}
                    fallback={userInitials}
                    size={80}
                    className="shadow-lg"
                  />
                  <div
                    className={`absolute -right-1 -bottom-1 h-6 w-6 rounded-full border-4 border-white shadow-md ${
                      user.status === 'active' ? 'bg-green-500' : 'bg-red-500'
                    }`}
                  />
                </div>

                <div>
                  <h1 className="mb-1 text-2xl font-bold text-gray-900">
                    {user.first_name} {user.last_name}
                  </h1>
                  <div className="mb-2 flex items-center text-gray-600">
                    <FaEnvelope className="mr-2 h-4 w-4 text-blue-600" />
                    <span>{user.email}</span>
                  </div>
                </div>
              </div>

              {/* Right Side - Job Info & Status */}
              <div className="space-y-2 text-right">
                {user.job_title && (
                  <div className="flex items-center justify-end text-gray-700">
                    <FaUserTie className="mr-2 h-4 w-4 text-purple-600" />
                    <span className="font-medium">{user.job_title.name}</span>
                  </div>
                )}

                {user.department && (
                  <div className="flex items-center justify-end text-gray-600">
                    <FaBuilding className="mr-2 h-4 w-4 text-green-600" />
                    <span>{user.department.name}</span>
                  </div>
                )}

                <div className="flex items-center justify-end space-x-2 pt-2">
                  <span
                    className={`inline-flex items-center rounded-full px-3 py-1 text-xs font-medium ${
                      user.status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {user.status === 'active' ? '✓ Aktif' : '✗ Pasif'}
                  </span>

                  {user.email_verified_at && (
                    <span className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800">
                      <FaShieldAlt className="mr-1 h-3 w-3" />
                      Doğrulandı
                    </span>
                  )}
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Content Sections */}
        <div className="space-y-6">
          <div className="flex flex-col gap-6 lg:flex-row lg:items-start">
            {/* Left Column */}
            <div className="flex-1 space-y-6">
              <Card className="border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md">
                <CardHeader className="border-b border-gray-100 pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-red-100">
                        <FaKey className="h-5 w-5 text-red-600" />
                      </div>
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        Şifre İşlemleri
                      </CardTitle>
                    </div>
                    <Link href={ROUTES.PROFILE.PASSWORD}>
                      <Button
                          type="primary"
                          size="small"
                          icon={<FaCog />}
                          className="flex items-center"
                      >
                        Yönet
                      </Button>
                    </Link>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Şifre Güvenliği</p>
                      <p className="text-sm text-gray-600">Son değiştirilme: 15.10.2024</p>
                    </div>
                    <div className="flex items-center">
                      <FaShieldAlt className="mr-2 text-green-500" />
                      <span className="font-medium text-green-600">Güçlü</span>
                    </div>
                  </div>
                  <Button type="primary" className="w-full">
                    Şifre Değiştir
                  </Button>
                  <Button type="default" className="w-full">
                    İki Faktörlü Doğrulama
                  </Button>
                </CardContent>
              </Card>
              <Card className="border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md">
                <CardHeader className="border-b border-gray-100 pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
                        <FaIdCard className="h-5 w-5 text-blue-600" />
                      </div>
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        Kimlik Bilgileri ({allIdentities.length})
                      </CardTitle>
                    </div>
                    <Link href={ROUTES.PROFILE.IDENTITY.INDEX}>
                      <Button
                        type="primary"
                        size="small"
                        icon={<FaCog />}
                        className="flex items-center"
                      >
                        Yönet
                      </Button>
                    </Link>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  {allIdentities.length > 0 ? (
                    <div className="space-y-4">
                      {allIdentities.map((identity, index) => (
                        <div key={identity.id} className={`${index > 0 ? 'border-t pt-4' : ''}`}>
                          <div className="mb-2 flex items-center justify-between">
                            <span className="text-sm font-medium text-gray-700">
                              Kimlik {index + 1}
                            </span>
                            {identity.is_default && (
                              <span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                                Varsayılan
                              </span>
                            )}
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Kimlik No:</span>
                            <span className="font-medium">
                              {maskIdentityNumber(identity.identity_number)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Uyruk:</span>
                            <span className="font-medium">{identity.nationality_code}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-4 text-center">
                      <span className="text-gray-500">Henüz kimlik bilgisi eklenmemiş</span>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md">
                <CardHeader className="border-b border-gray-100 pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-green-100">
                        <FaHome className="h-5 w-5 text-green-600" />
                      </div>
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        Adres Bilgileri ({allAddresses.length})
                      </CardTitle>
                    </div>
                    <Link href={ROUTES.PROFILE.ADDRESS.INDEX}>
                      <Button
                        type="primary"
                        size="small"
                        icon={<FaCog />}
                        className="flex items-center"
                      >
                        Yönet
                      </Button>
                    </Link>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {allAddresses.length > 0 ? (
                    <div className="space-y-4">
                      {allAddresses.map((address, index) => (
                        <div key={address.id} className={`${index > 0 ? 'border-t pt-4' : ''}`}>
                          <div className="mb-2 flex items-center justify-between">
                            <h4 className="flex items-center font-semibold text-gray-700">
                              <FaMapMarkerAlt className="mr-1 text-green-500" />
                              {address.type === 'home'
                                ? 'Ev Adresi'
                                : address.type === 'work'
                                  ? 'İş Adresi'
                                  : 'Diğer Adres'}
                            </h4>
                            {address.is_default && (
                              <span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                                Varsayılan
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">{address.address_line_1}</p>
                          {address.address_line_2 && (
                            <p className="text-sm text-gray-600">{address.address_line_2}</p>
                          )}
                          <p className="text-sm text-gray-600">
                            {address.neighborhood_street && `${address.neighborhood_street}, `}
                            {address.city || 'Bilinmiyor'} {address.postal_code || ''}
                          </p>
                          <p className="text-xs text-gray-500">{address.country_code}</p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-4 text-center">
                      <p className="text-gray-500">Henüz adres bilgisi eklenmemiş.</p>
                      <Link href={ROUTES.PROFILE.ADDRESS.CREATE}>
                        <Button type="link" size="small" className="mt-2">
                          Adres Ekle
                        </Button>
                      </Link>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md">
                <CardHeader className="border-b border-gray-100 pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-orange-100">
                        <FaPhone className="h-5 w-5 text-orange-600" />
                      </div>
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        İletişim Bilgileri ({allPhones.length})
                      </CardTitle>
                    </div>
                    <Link href={ROUTES.PROFILE.PHONE.INDEX}>
                      <Button
                        type="primary"
                        size="small"
                        icon={<FaCog />}
                        className="flex items-center"
                      >
                        Yönet
                      </Button>
                    </Link>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {allPhones.length > 0 ? (
                    <div className="space-y-4">
                      {allPhones.map((phone, index) => (
                        <div key={phone.id} className={`${index > 0 ? 'border-t pt-4' : ''}`}>
                          <div className="mb-2 flex items-center justify-between">
                            <h4 className="flex items-center font-semibold text-gray-700">
                              <FaPhone className="mr-1 text-orange-500" />
                              {phone.type === 'home'
                                ? 'Ev Telefonu'
                                : phone.type === 'mobile'
                                  ? 'Cep Telefonu'
                                  : 'Acil Telefon'}
                            </h4>
                            {phone.is_default && (
                              <span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                                Varsayılan
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">
                            {phone.country_code} {phone.phone_number}
                          </p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-4 text-center">
                      <p className="text-gray-500">Henüz telefon bilgisi eklenmemiş.</p>
                      <Link href={ROUTES.PROFILE.PHONE.CREATE}>
                        <Button type="link" size="small" className="mt-2">
                          Telefon Ekle
                        </Button>
                      </Link>
                    </div>
                  )}
                  <hr />
                  <div className="flex justify-between">
                    <span className="text-gray-600">Email:</span>
                    <span className="font-medium">{user.email}</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right Column */}
            <div className="flex-1 space-y-6">
              <Card className="border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md">
                <CardHeader className="border-b border-gray-100 pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100">
                        <FaPassport className="h-5 w-5 text-purple-600" />
                      </div>
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        Pasaport Bilgileri ({allPassports.length})
                      </CardTitle>
                    </div>
                    <Link href={ROUTES.PROFILE.PASSPORT.INDEX}>
                      <Button
                        type="primary"
                        size="small"
                        icon={<FaCog />}
                        className="flex items-center"
                      >
                        Yönet
                      </Button>
                    </Link>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {allPassports.length > 0 ? (
                    <div className="space-y-4">
                      {allPassports.map((passport, index) => (
                        <div key={passport.id} className={`${index > 0 ? 'border-t pt-4' : ''}`}>
                          <div className="mb-2 flex items-center justify-between">
                            <h4 className="flex items-center font-semibold text-gray-700">
                              <FaPassport className="mr-1 text-purple-500" />
                              Pasaport {index + 1}
                            </h4>
                            {passport.is_default && (
                              <span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                                Varsayılan
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">{passport.passport_number}</p>
                          <p className="text-xs text-gray-500">{passport.nationality}</p>
                          <div className="mt-2 text-xs text-gray-500">
                            Geçerlilik: {new Date(passport.expiry_date).toLocaleDateString('tr-TR')}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-4 text-center">
                      <p className="text-gray-500">Henüz pasaport bilgisi eklenmemiş.</p>
                      <Link href={ROUTES.PROFILE.PASSPORT.CREATE}>
                        <Button type="link" size="small" className="mt-2">
                          Pasaport Ekle
                        </Button>
                      </Link>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md">
                <CardHeader className="border-b border-gray-100 pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-teal-100">
                        <FaPassport className="h-5 w-5 text-teal-600" />
                      </div>
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        Vize Bilgileri ({allVisas.length})
                      </CardTitle>
                    </div>
                    <Link href={ROUTES.PROFILE.VISA.INDEX}>
                      <Button
                        type="primary"
                        size="small"
                        icon={<FaCog />}
                        className="flex items-center"
                      >
                        Yönet
                      </Button>
                    </Link>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {allVisas.length > 0 ? (
                    <div className="space-y-4">
                      {allVisas.map((visa, index) => (
                        <div key={visa.id} className={`${index > 0 ? 'border-t pt-4' : ''}`}>
                          <div className="mb-2 flex items-center justify-between">
                            <h4 className="flex items-center font-semibold text-gray-700">
                              <FaPassport className="mr-1 text-teal-500" />
                              Vize {index + 1}
                            </h4>
                            {visa.is_default && (
                              <span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                                Varsayılan
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">{visa.document_number}</p>
                          <p className="text-xs text-gray-500">
                            {visa.country} - {visa.visa_type === 'tourist' ? 'Turistik' : 'İş'}
                          </p>
                          <div className="mt-2 text-xs text-gray-500">
                            Geçerlilik: {new Date(visa.end_date).toLocaleDateString('tr-TR')}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-4 text-center">
                      <p className="text-gray-500">Henüz vize bilgisi eklenmemiş.</p>
                      <Link href={ROUTES.PROFILE.VISA.CREATE}>
                        <Button type="link" size="small" className="mt-2">
                          Vize Ekle
                        </Button>
                      </Link>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md">
                <CardHeader className="border-b border-gray-100 pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-lg bg-yellow-100">
                        <FaCreditCard className="h-5 w-5 text-yellow-600" />
                      </div>
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        Sadakat Programı ({allLoyalties.length})
                      </CardTitle>
                    </div>
                    <Link href={ROUTES.PROFILE.LOYALTY.INDEX}>
                      <Button
                        type="primary"
                        size="small"
                        icon={<FaCog />}
                        className="flex items-center"
                      >
                        Yönet
                      </Button>
                    </Link>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {allLoyalties.length > 0 ? (
                    <div className="space-y-4">
                      {allLoyalties.map((loyalty, index) => (
                        <div key={loyalty.id} className={`${index > 0 ? 'border-t pt-4' : ''}`}>
                          <div className="mb-2 flex items-center justify-between">
                            <h4 className="flex items-center font-semibold text-gray-700">
                              <FaCreditCard className="mr-1 text-yellow-500" />
                              {loyalty.airline?.airline_name || 'Bilinmeyen Havayolu'}
                            </h4>
                            {loyalty.is_default && (
                              <span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                                Varsayılan
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">Kart No: {loyalty.card_number}</p>
                          <p className="text-xs text-gray-500">
                            {loyalty.airline?.airline_code || '-'}
                          </p>
                          <div className="mt-2 text-xs text-gray-500">
                            Geçerlilik: {new Date(loyalty.end_date).toLocaleDateString('tr-TR')}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-4 text-center">
                      <p className="text-gray-500">Henüz sadakat programı eklenmemiş.</p>
                      <Link href={ROUTES.PROFILE.LOYALTY.CREATE}>
                        <Button type="link" size="small" className="mt-2">
                          Sadakat Programı Ekle
                        </Button>
                      </Link>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileContent;
