import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import ProfileContent from '../ProfileContent';
import { getUserById } from '@/app/services/user.service';

type Props = {
  params: Promise<{
    id: number;
  }>;
};

const ProfilePage = async ({ params }: Props) => {
  const { id } = await params;

  const user = await getUserById(id);

  if (!user.status) {
    return (
      <div className="flex h-64 items-center justify-center">
        <p className="text-gray-500">Kullanıcı bilgileri yüklenemedi.</p>
      </div>
    );
  }

  return (
    <>
      <HeaderBreadcrumb content="Varsayılan Profil Bilgileri" links={[{ title: 'Profil' }]} />
      <ProfileContent user={user.data} />
    </>
  );
};

export default ProfilePage;
